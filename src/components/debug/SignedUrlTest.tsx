import { useState } from 'react';
import { supabase } from '../../lib/supabase';

export const SignedUrlTest = () => {
  const [testUrl, setTestUrl] = useState('');
  const [result, setResult] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const testSignedUrl = async () => {
    if (!testUrl) return;
    
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('Testing signed URL for:', testUrl);
      
      // Extract the file path from the full URL
      const urlParts = testUrl.split('/');
      const bucketIndex = urlParts.findIndex(part => part === 'verbrauchsrechnungen');

      if (bucketIndex === -1) {
        throw new Error('Bucket not found in URL');
      }

      const filePath = urlParts.slice(bucketIndex + 1).join('/');
      console.log('Extracted file path:', filePath);

      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      console.log('Current user:', user?.id);

      // Try to create signed URL
      const { data, error } = await supabase.storage
        .from('verbrauchsrechnungen')
        .createSignedUrl(filePath, 3600);

      if (error) {
        throw error;
      }

      console.log('Signed URL created successfully:', data.signedUrl);
      setResult(data.signedUrl);
    } catch (err) {
      console.error('Error creating signed URL:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-4 border border-gray-300 rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold mb-4">Signed URL Test</h3>
      
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Test URL:
        </label>
        <input
          type="text"
          value={testUrl}
          onChange={(e) => setTestUrl(e.target.value)}
          placeholder="https://peuelelfodoiyvmjscfg.supabase.co/storage/v1/object/public/verbrauchsrechnungen/..."
          className="w-full px-3 py-2 border border-gray-300 rounded-md"
        />
      </div>

      <button
        onClick={testSignedUrl}
        disabled={!testUrl || isLoading}
        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400"
      >
        {isLoading ? 'Testing...' : 'Test Signed URL'}
      </button>

      {error && (
        <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          <strong>Error:</strong> {error}
        </div>
      )}

      {result && (
        <div className="mt-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
          <strong>Success!</strong>
          <br />
          <a href={result} target="_blank" rel="noopener noreferrer" className="underline">
            Test the signed URL
          </a>
        </div>
      )}
    </div>
  );
};
